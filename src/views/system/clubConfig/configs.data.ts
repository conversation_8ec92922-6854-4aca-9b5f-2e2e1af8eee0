import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE } from '@/utils/dict'
import { getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: '主键编号',
    dataIndex: 'id',
    width: 160,
    defaultHidden: true,
  },
  {
    title: '配置key',
    dataIndex: 'confKey',
    width: 160,
    customRender: ({ text }) => {
      const options = getDictOptions(DICT_TYPE.CLUB_CONFIG_KEY)
      const option = options.find(item => item.value === text)
      return option?.label || text
    }
  },
  {
    title: '配置值',
    dataIndex: 'confVal',
    width: 300,
    customRender: ({ text }) => {
      try {
        const conf = JSON.parse(text);

        // 检查是否为动态表单配置
        if (conf.formSchema && conf.defaultValues) {
          // 动态表单配置
          const currentValues = conf.currentValues || conf.defaultValues
          const displayFields = Object.keys(currentValues).slice(0, 2) // 显示前两个字段
          const displayText = displayFields.map(key => `${key}: ${currentValues[key]}`).join(', ')
          return `动态配置: ${displayText}${Object.keys(currentValues).length > 2 ? '...' : ''}`
        }
        return text;
      } catch {
        return text;
      }
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '配置key',
    field: 'confKey',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_CONFIG_KEY, 'string'),
      placeholder: '请选择配置key'
    },
    colProps: { span: 8 }
  },
  {
    label: '配置值',
    field: 'confVal',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]

// 积分配置表单结构
export const pointConfigSchemas: FormSchema[] = [
  {
    label: '积分类型',
    field: 'pointType',
    component: 'Select', // 从 InputNumber 改为 Select
    required: true,
    componentProps: {
      // 使用 DICT_TYPE.MEMBER_POINT_BIZ_TYPE 字典选项
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE),
      placeholder: '请选择积分类型'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分名称',
    field: 'pointName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入积分名称'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分值',
    field: 'point',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入积分值'
    },
    colProps: { span: 12 }
  },
  {
    label: '限制次数',
    field: 'limitTimes',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入限制次数'
    },
    colProps: { span: 12 }
  },
  {
    label: '积分限制',
    field: 'pointLimit',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      placeholder: '请输入积分限制'
    },
    colProps: { span: 12 }
  }
]

// APP配置表单结构
export const appConfigSchemas: FormSchema[] = [
  {
    label: 'LOGO',
    field: 'logo',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入LOGO文件名'
    },
    colProps: { span: 12 }
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入APP名称'
    },
    colProps: { span: 12 }
  }
]

// 动态表单配置接口定义
export interface DynamicFormConfig {
  formSchema: FormSchema[]    // 表单结构定义
  defaultValues: Record<string, any>  // 默认值
  currentValues?: Record<string, any> // 当前值（可选）
}

// 示例：将APP配置转换为JSON格式存储在confVal中
export const getAppConfigJSON = (): string => {
  const config: DynamicFormConfig = {
    formSchema: [
      {
        label: 'LOGO',
        field: 'logo',
        component: 'Input',
        required: true,
        componentProps: {
          placeholder: '请输入LOGO文件名'
        },
        colProps: { span: 12 }
      },
      {
        label: '名称',
        field: 'name',
        component: 'Input',
        required: true,
        componentProps: {
          placeholder: '请输入APP名称'
        },
        colProps: { span: 12 }
      }
    ],
    defaultValues: {
      logo: 'default.png',
      name: '默认应用名称'
    }
  }
  return JSON.stringify(config)
}

// 解析动态表单配置
export const parseDynamicFormConfig = (confVal: string): DynamicFormConfig | null => {
  try {
    const config = JSON.parse(confVal)
    // 检查是否为动态表单配置格式
    if (config.formSchema && Array.isArray(config.formSchema)) {
      return config as DynamicFormConfig
    }
    return null
  } catch (error) {
    console.error('解析动态表单配置失败:', error)
    return null
  }
}

// 创建动态表单配置的工具函数
export const createDynamicFormConfig = (
  formSchema: FormSchema[],
  defaultValues: Record<string, any>,
  currentValues?: Record<string, any>
): string => {
  const config: DynamicFormConfig = {
    formSchema,
    defaultValues,
    currentValues
  }
  return JSON.stringify(config)
}

// 示例：创建一个完整的APP配置
export const createAppDynamicConfig = (): string => {
  return createDynamicFormConfig(
    [
      {
        label: 'LOGO',
        field: 'logo',
        component: 'Input',
        required: true,
        componentProps: {
          placeholder: '请输入LOGO文件名'
        },
        colProps: { span: 12 }
      },
      {
        label: '名称',
        field: 'name',
        component: 'Input',
        required: true,
        componentProps: {
          placeholder: '请输入APP名称'
        },
        colProps: { span: 12 }
      }
    ],
    {
      logo: 'default.png',
      name: '默认应用名称'
    }
  )
}

// 修改为函数形式，接收回调
export const getCreateFormSchema = (onChange?: (key: string) => void): FormSchema[] => [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Select',
    required: true,
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_CONFIG_KEY, 'string'),
      placeholder: '请选择配置key',
      onChange: onChange || (() => { })
    },
    colProps: { span: 24 }
  }
]

export const updateFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '配置key',
    field: 'confKey',
    component: 'Input',
    componentProps: {
      disabled: true
    },
    colProps: { span: 24 }
  }
]
