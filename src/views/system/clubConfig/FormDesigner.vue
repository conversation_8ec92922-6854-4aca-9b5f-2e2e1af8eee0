<script lang="ts" setup>
import { ref, computed } from 'vue'
import { BasicForm, useForm, type FormSchema } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { useMessage } from '@/hooks/web/useMessage'
import { createConfigs } from '@/api/club/config'
import { createDynamicFormConfig } from './configs.data'

defineOptions({ name: 'FormDesigner' })

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()

// 配置项类型选项
const configTypeOptions = [
  { label: '文本', value: 'text' },
  { label: '多行文本', value: 'textarea' },
  { label: '富文本', value: 'richtext' },
  { label: '图片', value: 'image' },
  { label: '状态开关', value: 'switch' }
]

// 当前选择的配置类型
const selectedConfigType = ref('')

// 基础配置表单
const baseConfigSchema: FormSchema[] = [
  {
    label: '配置项类型',
    field: 'configType',
    component: 'Select',
    required: true,
    componentProps: {
      options: configTypeOptions,
      placeholder: '请选择配置项类型',
      onChange: handleConfigTypeChange
    }
  },
  {
    label: '配置项名称',
    field: 'configName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置项名称',
      onChange: handleConfigNameChange
    }
  },
  {
    label: '配置Key',
    field: 'confKey',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置Key（系统自动生成）',
      disabled: true
    }
  }
]

// 动态参数值表单配置
const paramValueSchema = computed<FormSchema[]>(() => {
  if (!selectedConfigType.value) return []

  switch (selectedConfigType.value) {
    case 'text':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'Input',
          required: true,
          componentProps: {
            placeholder: '请输入文本内容'
          }
        }
      ]

    case 'textarea':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'InputTextArea',
          required: true,
          componentProps: {
            placeholder: '请输入多行文本内容',
            rows: 4
          }
        }
      ]

    case 'richtext':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'InputTextArea',
          required: true,
          componentProps: {
            placeholder: '请输入富文本内容（HTML格式）',
            rows: 6
          }
        }
      ]

    case 'image':
      return [
        {
          label: '图片',
          field: 'paramValue',
          component: 'FileUpload',
          required: true,
          componentProps: {
            maxCount: 1,
            fileType: 'image',
            hintText: '请上传图片'
          }
        }
      ]

    case 'switch':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'Switch',
          required: true,
          componentProps: {
            checkedChildren: '启用',
            unCheckedChildren: '禁用'
          }
        }
      ]

    default:
      return []
  }
})

const [registerBaseForm, { validate: validateBase, getFieldsValue: getBaseValues, setFieldsValue: setBaseValues }] = useForm({
  labelWidth: 120,
  schemas: baseConfigSchema,
  showActionButtonGroup: false
})

const [registerParamForm, { validate: validateParam, getFieldsValue: getParamValues, resetFields: resetParamForm, resetSchema }] = useForm({
  labelWidth: 120,
  schemas: paramValueSchema.value,
  showActionButtonGroup: false
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
  // 重置表单
  selectedConfigType.value = ''
  resetParamForm()
})

// 处理配置类型变化
function handleConfigTypeChange(value: string) {
  selectedConfigType.value = value
  generateConfigKey()

  // 重置并更新参数值表单结构
  resetSchema(paramValueSchema.value)
}

// 处理配置名称变化
function handleConfigNameChange(value: string) {
  generateConfigKey()
}

// 生成配置Key
function generateConfigKey() {
  const baseValues = getBaseValues()
  if (baseValues.configName && selectedConfigType.value) {
    const confKey = `${selectedConfigType.value.toUpperCase()}_${baseValues.configName.replace(/\s+/g, '_').toUpperCase()}`
    setBaseValues({ confKey })
  }
}



// 保存配置
async function saveConfig() {
  try {
    const baseValues = await validateBase()
    const paramValues = await validateParam()

    if (!selectedConfigType.value) {
      createMessage.warning('请选择配置项类型')
      return
    }

    // 构建表单配置
    const formSchema = getFormSchemaByType(selectedConfigType.value)
    const defaultValues = { paramValue: paramValues.paramValue }

    const configJson = createDynamicFormConfig(
      formSchema,
      defaultValues
    )

    await createConfigs({
      confKey: baseValues.confKey,
      confVal: configJson
    })

    closeModal()
    emit('success')
    createMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    createMessage.error('保存失败，请重试')
  }
}

// 根据配置类型生成表单结构
function getFormSchemaByType(configType: string): FormSchema[] {
  switch (configType) {
    case 'text':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'Input',
          required: true,
          componentProps: {
            placeholder: '请输入文本内容'
          }
        }
      ]

    case 'textarea':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'InputTextArea',
          required: true,
          componentProps: {
            placeholder: '请输入多行文本内容',
            rows: 4
          }
        }
      ]

    case 'richtext':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'InputTextArea',
          required: true,
          componentProps: {
            placeholder: '请输入富文本内容（HTML格式）',
            rows: 6
          }
        }
      ]

    case 'image':
      return [
        {
          label: '图片',
          field: 'paramValue',
          component: 'FileUpload',
          required: true,
          componentProps: {
            maxCount: 1,
            fileType: 'image',
            hintText: '请上传图片'
          }
        }
      ]

    case 'switch':
      return [
        {
          label: '参数值',
          field: 'paramValue',
          component: 'Switch',
          required: true,
          componentProps: {
            checkedChildren: '启用',
            unCheckedChildren: '禁用'
          }
        }
      ]

    default:
      return []
  }
}


</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="新增系统配置参数"
    @register="registerModal"
    @ok="saveConfig"
    width="800px"
    :okText="'保存'"
  >
    <div class="form-designer">
      <div class="config-panel">
        <h4>基础配置</h4>
        <BasicForm @register="registerBaseForm" />
      </div>
    </div>
  </BasicModal>
</template>

<style scoped>
.form-designer {
  padding: 20px 0;
}

.config-panel,
.param-panel {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
  min-height: 350px;
}

.param-content {
  min-height: 280px;
  display: flex;
  align-items: flex-start;
}

.param-form {
  width: 100%;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.config-description {
  margin-top: 20px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.config-description ul {
  margin: 0;
  padding-left: 20px;
}

.config-description li {
  margin-bottom: 8px;
  line-height: 1.6;
}

h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}
</style>
