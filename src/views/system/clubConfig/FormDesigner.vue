<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { BasicForm, useForm, type FormSchema } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { useMessage } from '@/hooks/web/useMessage'
import { createConfigs } from '@/api/club/config'
import { createDynamicFormConfig, type DynamicFormConfig } from './configs.data'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

defineOptions({ name: 'FormDesigner' })

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()

// 表单字段类型选项
const fieldTypeOptions = [
  { label: '文本输入', value: 'Input' },
  { label: '多行文本', value: 'InputTextArea' },
  { label: '数字输入', value: 'InputNumber' },
  { label: '下拉选择', value: 'Select' },
  { label: '单选框组', value: 'RadioGroup' },
  { label: '复选框组', value: 'CheckboxGroup' },
  { label: '开关', value: 'Switch' },
  { label: '日期选择', value: 'DatePicker' },
  { label: '时间选择', value: 'TimePicker' },
  { label: '文件上传', value: 'FileUpload' },
  { label: '分割线', value: 'Divider' }
]

// 当前设计的表单配置
const formConfig = reactive<DynamicFormConfig>({
  formSchema: [],
  defaultValues: {}
})

// 当前编辑的字段
const currentField = ref<FormSchema | null>(null)
const currentFieldIndex = ref(-1)

// 基础配置表单
const baseConfigSchema: FormSchema[] = [
  {
    label: '配置Key',
    field: 'confKey',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入配置Key'
    }
  }
]

// 字段编辑表单
const fieldEditSchema = computed<FormSchema[]>(() => [
  {
    label: '字段标签',
    field: 'label',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入字段标签'
    }
  },
  {
    label: '字段名称',
    field: 'field',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入字段名称（英文）'
    }
  },
  {
    label: '字段类型',
    field: 'component',
    component: 'Select',
    required: true,
    componentProps: {
      options: fieldTypeOptions,
      placeholder: '请选择字段类型'
    }
  },
  {
    label: '是否必填',
    field: 'required',
    component: 'Switch',
    componentProps: {
      checkedChildren: '必填',
      unCheckedChildren: '选填'
    }
  },
  {
    label: '占位提示',
    field: 'placeholder',
    component: 'Input',
    componentProps: {
      placeholder: '请输入占位提示文字'
    }
  },
  {
    label: '默认值',
    field: 'defaultValue',
    component: 'Input',
    componentProps: {
      placeholder: '请输入默认值'
    }
  },
  {
    label: '选项配置',
    field: 'options',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入选项配置（JSON格式）\n例如：[{"label":"选项1","value":"1"},{"label":"选项2","value":"2"}]',
      rows: 3
    },
    ifShow: ({ values }) => ['Select', 'RadioGroup', 'CheckboxGroup'].includes(values.component)
  },
  {
    label: '列宽度',
    field: 'span',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 24,
      placeholder: '1-24'
    },
    defaultValue: 12
  }
])

const [registerBaseForm, { validate: validateBase, getFieldsValue: getBaseValues }] = useForm({
  labelWidth: 100,
  schemas: baseConfigSchema,
  showActionButtonGroup: false
})

const [registerFieldForm, { validate: validateField, setFieldsValue: setFieldValues, resetFields: resetFieldForm }] = useForm({
  labelWidth: 100,
  schemas: fieldEditSchema.value,
  showActionButtonGroup: false
})

// 预览表单
const [registerPreviewForm] = useForm({
  labelWidth: 120,
  schemas: formConfig.formSchema,
  showActionButtonGroup: false
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
  // 重置表单
  formConfig.formSchema = []
  formConfig.defaultValues = {}
  currentField.value = null
  currentFieldIndex.value = -1
})

// 添加字段
function addField() {
  currentField.value = {
    label: '',
    field: '',
    component: 'Input',
    required: false,
    componentProps: {
      placeholder: ''
    },
    colProps: { span: 12 }
  }
  currentFieldIndex.value = -1
  resetFieldForm()
}

// 编辑字段
function editField(index: number) {
  const field = formConfig.formSchema[index]
  currentField.value = { ...field }
  currentFieldIndex.value = index
  
  // 设置表单值
  setFieldValues({
    label: field.label,
    field: field.field,
    component: field.component,
    required: field.required || false,
    placeholder: field.componentProps?.placeholder || '',
    defaultValue: formConfig.defaultValues[field.field] || '',
    span: field.colProps?.span || 12,
    options: field.componentProps?.options ? JSON.stringify(field.componentProps.options, null, 2) : ''
  })
}

// 保存字段
async function saveField() {
  try {
    const values = await validateField()

    const fieldConfig: FormSchema = {
      label: values.label,
      field: values.field,
      component: values.component,
      required: values.required,
      colProps: { span: values.span }
    }

    // 构建 componentProps
    const componentProps: any = {}

    if (values.placeholder) {
      componentProps.placeholder = values.placeholder
    }

    // 处理选项配置
    if (values.options && ['Select', 'RadioGroup', 'CheckboxGroup'].includes(values.component)) {
      try {
        componentProps.options = JSON.parse(values.options)
      } catch (error) {
        createMessage.error('选项配置格式错误，请输入正确的JSON格式')
        return
      }
    }

    // 处理开关组件的特殊配置
    if (values.component === 'Switch') {
      componentProps.checkedChildren = '是'
      componentProps.unCheckedChildren = '否'
    }

    // 处理数字输入的特殊配置
    if (values.component === 'InputNumber') {
      componentProps.min = 0
    }

    // 处理多行文本的特殊配置
    if (values.component === 'InputTextArea') {
      componentProps.rows = 3
    }

    if (Object.keys(componentProps).length > 0) {
      fieldConfig.componentProps = componentProps
    }

    if (currentFieldIndex.value >= 0) {
      // 编辑模式
      formConfig.formSchema[currentFieldIndex.value] = fieldConfig
    } else {
      // 新增模式
      formConfig.formSchema.push(fieldConfig)
    }

    // 设置默认值
    if (values.defaultValue !== undefined && values.defaultValue !== '') {
      let defaultValue = values.defaultValue

      // 根据组件类型转换默认值
      if (values.component === 'InputNumber') {
        defaultValue = Number(defaultValue)
      } else if (values.component === 'Switch') {
        defaultValue = defaultValue === 'true' || defaultValue === true
      }

      formConfig.defaultValues[values.field] = defaultValue
    }

    currentField.value = null
    currentFieldIndex.value = -1
    resetFieldForm()

    createMessage.success('字段保存成功')
  } catch (error) {
    console.error('保存字段失败:', error)
  }
}

// 删除字段
function deleteField(index: number) {
  const field = formConfig.formSchema[index]
  formConfig.formSchema.splice(index, 1)
  delete formConfig.defaultValues[field.field]
  createMessage.success('字段删除成功')
}

// 移动字段
function moveField(index: number, direction: 'up' | 'down') {
  const newIndex = direction === 'up' ? index - 1 : index + 1
  if (newIndex < 0 || newIndex >= formConfig.formSchema.length) return
  
  const temp = formConfig.formSchema[index]
  formConfig.formSchema[index] = formConfig.formSchema[newIndex]
  formConfig.formSchema[newIndex] = temp
}

// 保存配置
async function saveConfig() {
  try {
    const baseValues = await validateBase()
    
    if (formConfig.formSchema.length === 0) {
      createMessage.warning('请至少添加一个字段')
      return
    }

    const configJson = createDynamicFormConfig(
      formConfig.formSchema,
      formConfig.defaultValues
    )

    await createConfigs({
      confKey: baseValues.confKey,
      confVal: configJson
    })

    closeModal()
    emit('success')
    createMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    createMessage.error('保存失败，请重试')
  }
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="动态表单设计器"
    @register="registerModal"
    @ok="saveConfig"
    width="1200px"
    :okText="'保存配置'"
  >
    <div class="form-designer">
      <a-row :gutter="16">
        <!-- 左侧：配置区域 -->
        <a-col :span="8">
          <div class="config-panel">
            <h4>基础配置</h4>
            <BasicForm @register="registerBaseForm" />
            
            <a-divider />
            
            <div class="field-list">
              <div class="field-list-header">
                <h4>字段列表</h4>
                <a-button type="primary" size="small" @click="addField">
                  添加字段
                </a-button>
              </div>
              
              <div class="field-items">
                <div
                  v-for="(field, index) in formConfig.formSchema"
                  :key="index"
                  class="field-item"
                >
                  <div class="field-info">
                    <div class="field-label">{{ field.label }}</div>
                    <div class="field-type">{{ field.component }}</div>
                  </div>
                  <div class="field-actions">
                    <a-button size="small" @click="editField(index)">编辑</a-button>
                    <a-button size="small" @click="moveField(index, 'up')" :disabled="index === 0">↑</a-button>
                    <a-button size="small" @click="moveField(index, 'down')" :disabled="index === formConfig.formSchema.length - 1">↓</a-button>
                    <a-button size="small" danger @click="deleteField(index)">删除</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 中间：字段编辑 -->
        <a-col :span="8">
          <div class="field-editor" v-if="currentField">
            <h4>{{ currentFieldIndex >= 0 ? '编辑字段' : '新增字段' }}</h4>
            <BasicForm @register="registerFieldForm" />
            <div class="field-editor-actions">
              <a-button type="primary" @click="saveField">保存字段</a-button>
              <a-button @click="currentField = null">取消</a-button>
            </div>
          </div>
          <div v-else class="field-editor-empty">
            <a-empty description="请选择或添加字段进行编辑" />
          </div>
        </a-col>

        <!-- 右侧：预览区域 -->
        <a-col :span="8">
          <div class="preview-panel">
            <h4>表单预览</h4>
            <div class="preview-content">
              <BasicForm @register="registerPreviewForm" v-if="formConfig.formSchema.length > 0" />
              <a-empty v-else description="暂无字段，请添加字段" />
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </BasicModal>
</template>

<style scoped>
.form-designer {
  height: 600px;
}

.config-panel,
.field-editor,
.preview-panel {
  height: 100%;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.field-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.field-items {
  max-height: 300px;
  overflow-y: auto;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.field-info {
  flex: 1;
}

.field-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.field-type {
  font-size: 12px;
  color: #666;
}

.field-actions {
  display: flex;
  gap: 4px;
}

.field-actions .ant-btn {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
}

.field-editor-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.field-editor-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.preview-content {
  background: white;
  padding: 16px;
  border-radius: 4px;
  min-height: 400px;
}

h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}
</style>
