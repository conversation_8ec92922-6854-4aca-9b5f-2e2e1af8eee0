<script lang="ts" setup>
import { ref, unref, nextTick } from 'vue'
import { getCreateFormSchema, updateFormSchema, pointConfigSchemas, appConfigSchemas, parseDynamicFormConfig, type DynamicFormConfig } from './configs.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { createConfigs, getConfigs, updateConfigs } from '@/api/club/config'
import { DICT_TYPE } from '@/utils/dict'
import { getDictOptions } from '@/utils/dict'

defineOptions({ name: 'ClubConfigsModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)
const currentConfKey = ref('')

// 动态创建表单 schema
const createFormSchema = ref(getCreateFormSchema(handleConfKeyChange))

// 修复类型错误：显式定义表单配置对象
const formConfig = {
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: createFormSchema.value,
  showActionButtonGroup: false as const, // 使用 const 断言解决类型问题
  actionColOptions: { span: 23 }
}

const [registerForm, {
  setFieldsValue,
  getFieldsValue,
  resetFields,
  resetSchema,
  validate,
  appendSchemaByField,
  removeSchemaByField,
  updateSchema
}] = useForm(formConfig)

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  isUpdate.value = !!data?.isUpdate

  if (unref(isUpdate)) {
    // 编辑模式 - 先重置为更新表单
    resetSchema(updateFormSchema)

    // 获取数据
    const res = await getConfigs(data.record.id)
    currentConfKey.value = res.confKey

    // 等待表单更新完成
    await nextTick()

    try {
      // 尝试解析为动态表单配置
      const dynamicConfig = parseDynamicFormConfig(res.confVal)

      if (dynamicConfig) {
        // 如果是动态表单配置，使用新的处理方式
        handleDynamicFormConfig(res.confVal)

        // 设置基础字段值
        setFieldsValue({
          id: res.id,
          confKey: res.confKey
        })
      } else {
        // 兼容旧的配置方式
        // 根据confKey添加对应的表单字段
        if (res.confKey === 'CLUB_POINT_CONFIG') {
          appendSchemaByField(pointConfigSchemas, 'confKey')
        } else if (res.confKey === 'CLUB_APP_INDEX_CONFIG') {
          appendSchemaByField(appConfigSchemas, 'confKey')
        }

        // 解析JSON配置值
        const confVal = res.confVal ? JSON.parse(res.confVal) : {}

        // 设置表单值（包含动态字段）
        setFieldsValue({
          ...res,
          ...confVal
        })
      }
    } catch (e) {
      console.error('解析 confVal 失败:', e)
      setFieldsValue(res)
    }
  } else {
    // 新建模式
    currentConfKey.value = ''
    createFormSchema.value = getCreateFormSchema(handleConfKeyChange)
    resetSchema(createFormSchema.value)

    // 添加监听器，等待DOM更新后绑定事件
    nextTick(() => {
      // 手动触发一次初始变更（如果需要）
      const formValues = getFieldsValue()
      const initialKey = formValues?.confKey
      if (initialKey) handleConfKeyChange(initialKey)
    })
  }
})

// 处理confKey变化
function handleConfKeyChange(key: string) {
  console.log('配置Key变更:', key)
  currentConfKey.value = key
  // 先移除所有动态添加的字段
  const allFields = [...pointConfigSchemas, ...appConfigSchemas].map(item => item.field)
  removeSchemaByField(allFields)

  // 添加分隔线
  appendSchemaByField([{
    label: '配置详情',
    field: 'configDivider',
    component: 'Divider',
    colProps: { span: 24 }
  }], 'confKey')

  // 根据选择的key添加对应的字段
  if (key === 'CLUB_POINT_CONFIG') {
    appendSchemaByField(pointConfigSchemas, 'configDivider')
    // 设置默认值
    setFieldsValue({
      pointType: 101,
      point: 0,
      limitTimes: 0,
      pointLimit: 0
    })
  } else if (key === 'CLUB_APP_INDEX_CONFIG') {
    appendSchemaByField(appConfigSchemas, 'configDivider')
    // 设置默认值
    setFieldsValue({
      logo: 'default.png'
    })
  }
}

// 处理动态表单配置
function handleDynamicFormConfig(confVal: string) {
  console.log('处理动态表单配置:', confVal)

  // 解析动态表单配置
  const dynamicConfig = parseDynamicFormConfig(confVal)
  if (!dynamicConfig) {
    console.error('无法解析动态表单配置')
    return
  }

  // 先移除所有动态添加的字段
  const allFields = [...pointConfigSchemas, ...appConfigSchemas].map(item => item.field)
  removeSchemaByField(allFields)

  // 添加分隔线
  appendSchemaByField([{
    label: '配置详情',
    field: 'configDivider',
    component: 'Divider',
    colProps: { span: 24 }
  }], 'confKey')

  // 动态添加表单字段
  appendSchemaByField(dynamicConfig.formSchema, 'configDivider')

  // 设置默认值或当前值
  const valuesToSet = dynamicConfig.currentValues || dynamicConfig.defaultValues
  if (valuesToSet) {
    setFieldsValue(valuesToSet)
  }
}

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })

    let confVal: string

    // 检查是否为动态表单配置
    if (isDynamicFormConfig(values)) {
      // 处理动态表单配置
      confVal = handleDynamicFormSubmit(values)
    } else {
      // 兼容旧的配置方式
      let confValObj = {}
      if (values.confKey === 'CLUB_POINT_CONFIG') {
        confValObj = {
          pointType: values.pointType,
          pointName: values.pointName,
          point: values.point,
          limitTimes: values.limitTimes,
          pointLimit: values.pointLimit
        }
      } else if (values.confKey === 'CLUB_APP_INDEX_CONFIG') {
        confValObj = {
          logo: values.logo,
          name: values.name
        }
      }
      confVal = JSON.stringify(confValObj)
    }

    const submitData = {
      id: values.id,
      confKey: values.confKey,
      confVal: confVal
    }

    if (unref(isUpdate)) {
      await updateConfigs(submitData)
    } else {
      await createConfigs(submitData)
    }

    closeModal()
    emit('success')
    createMessage.success(t('common.saveSuccessText'))
  } catch (e) {
    console.error('提交失败:', e)
    createMessage.error(t('common.saveFailText'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}

// 判断是否为动态表单配置
function isDynamicFormConfig(values: any): boolean {
  // 这里可以根据具体的业务逻辑来判断
  // 比如检查是否存在特定的标识字段，或者根据confKey来判断
  return false // 暂时返回false，保持兼容性
}

// 处理动态表单提交
function handleDynamicFormSubmit(values: any): string {
  // 提取基础字段
  const { id, confKey, ...dynamicFields } = values

  // 构建动态表单配置
  const dynamicConfig: DynamicFormConfig = {
    formSchema: [], // 这里需要根据实际情况获取当前的表单结构
    defaultValues: {},
    currentValues: dynamicFields
  }

  return JSON.stringify(dynamicConfig)
}

// 暴露方法给表单使用
defineExpose({
  handleConfKeyChange
})
</script>

<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? t('action.edit') : t('action.create')" @register="registerModal"
    @ok="handleSubmit" width="700px">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<style scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
