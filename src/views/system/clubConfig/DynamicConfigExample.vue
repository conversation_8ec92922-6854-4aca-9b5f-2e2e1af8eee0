<script lang="ts" setup>
import { ref } from 'vue'
import { getAppConfigJSON, type DynamicFormConfig } from './configs.data'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { createConfigs } from '@/api/club/config'

defineOptions({ name: 'DynamicConfigExample' })

const { createMessage } = useMessage()

// 示例：创建一个动态表单配置并保存到数据库
const exampleConfig = ref<DynamicFormConfig>({
  formSchema: [
    {
      label: 'LOGO',
      field: 'logo',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入LOGO文件名'
      },
      colProps: { span: 12 }
    },
    {
      label: '名称',
      field: 'name',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入APP名称'
      },
      colProps: { span: 12 }
    },
    {
      label: '描述',
      field: 'description',
      component: 'InputTextArea',
      required: false,
      componentProps: {
        placeholder: '请输入描述信息',
        rows: 3
      },
      colProps: { span: 24 }
    },
    {
      label: '是否启用',
      field: 'enabled',
      component: 'Switch',
      required: true,
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用'
      },
      colProps: { span: 12 }
    }
  ],
  defaultValues: {
    logo: 'default.png',
    name: '默认应用名称',
    description: '这是一个示例配置',
    enabled: true
  }
})

// 预览表单配置
const [registerPreviewForm] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: exampleConfig.value.formSchema,
  showActionButtonGroup: false
})

// 保存动态配置到数据库
async function saveDynamicConfig() {
  try {
    const configData = {
      confKey: 'DYNAMIC_FORM_EXAMPLE',
      confVal: JSON.stringify(exampleConfig.value)
    }
    
    await createConfigs(configData)
    createMessage.success('动态配置保存成功！')
  } catch (error) {
    console.error('保存失败:', error)
    createMessage.error('保存失败，请重试')
  }
}

// 生成配置JSON
function generateConfigJSON() {
  return JSON.stringify(exampleConfig.value, null, 2)
}
</script>

<template>
  <div class="dynamic-config-example">
    <div class="config-section">
      <h3>动态表单配置示例</h3>
      <p>以下是一个动态表单配置的示例，展示了如何将表单结构和默认值以JSON格式存储：</p>
      
      <!-- 配置JSON展示 -->
      <div class="json-display">
        <h4>配置JSON（将存储在confVal字段中）：</h4>
        <pre>{{ generateConfigJSON() }}</pre>
      </div>
      
      <!-- 表单预览 -->
      <div class="form-preview">
        <h4>表单预览：</h4>
        <BasicForm @register="registerPreviewForm" />
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <a-button type="primary" @click="saveDynamicConfig">
          保存配置到数据库
        </a-button>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <h3>使用说明</h3>
      <ol>
        <li><strong>数据库存储</strong>：整个表单配置以JSON字符串形式存储在 <code>confVal</code> 字段中</li>
        <li><strong>表单结构</strong>：包含 <code>formSchema</code>（表单字段定义）和 <code>defaultValues</code>（默认值）</li>
        <li><strong>动态渲染</strong>：前端解析JSON配置，动态生成表单</li>
        <li><strong>数据提交</strong>：表单数据可以更新 <code>currentValues</code> 字段</li>
      </ol>
      
      <h4>JSON结构说明：</h4>
      <ul>
        <li><code>formSchema</code>：表单字段配置数组，包含字段类型、验证规则、组件属性等</li>
        <li><code>defaultValues</code>：各字段的默认值</li>
        <li><code>currentValues</code>：当前保存的值（可选，用于编辑时回显）</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.dynamic-config-example {
  padding: 20px;
}

.config-section {
  margin-bottom: 30px;
}

.json-display {
  margin: 20px 0;
}

.json-display pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

.form-preview {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}

.actions {
  margin: 20px 0;
}

.usage-guide {
  padding: 20px;
  background: #f0f2f5;
  border-radius: 4px;
}

.usage-guide h3 {
  margin-top: 0;
}

.usage-guide code {
  background: #fff;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}
</style>
