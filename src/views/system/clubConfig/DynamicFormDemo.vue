<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { BasicForm, useForm } from '@/components/Form'
import { useMessage } from '@/hooks/web/useMessage'
import { getConfigsPage } from '@/api/club/config'
import { parseDynamicFormConfig } from './configs.data'

defineOptions({ name: 'DynamicFormDemo' })

const { createMessage } = useMessage()

// 动态配置列表
const configList = ref([])
const selectedConfig = ref(null)
const currentFormSchema = ref([])

// 动态表单实例
const [registerDynamicForm, { validate, setFieldsValue, resetFields }] = useForm({
  labelWidth: 120,
  schemas: currentFormSchema.value,
  showActionButtonGroup: false
})

// 获取配置列表
async function loadConfigs() {
  try {
    const result = await getConfigsPage({})
    const configs = result.list || []
    
    // 过滤出动态表单配置
    configList.value = configs.filter(config => {
      const parsed = parseDynamicFormConfig(config.confVal)
      return parsed !== null
    }).map(config => ({
      ...config,
      parsedConfig: parseDynamicFormConfig(config.confVal)
    }))
  } catch (error) {
    console.error('加载配置失败:', error)
    createMessage.error('加载配置失败')
  }
}

// 选择配置
function selectConfig(config) {
  selectedConfig.value = config
  currentFormSchema.value = config.parsedConfig.formSchema
  
  // 重新注册表单
  resetFields()
  
  // 设置默认值
  if (config.parsedConfig.defaultValues) {
    setFieldsValue(config.parsedConfig.defaultValues)
  }
}

// 提交表单
async function handleSubmit() {
  try {
    const values = await validate()
    console.log('表单数据:', values)
    createMessage.success('表单提交成功！数据已打印到控制台')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
function handleReset() {
  resetFields()
  if (selectedConfig.value?.parsedConfig.defaultValues) {
    setFieldsValue(selectedConfig.value.parsedConfig.defaultValues)
  }
}

onMounted(() => {
  loadConfigs()
})
</script>

<template>
  <div class="dynamic-form-demo">
    <a-card title="动态表单演示" class="demo-card">
      <template #extra>
        <a-button @click="loadConfigs">刷新配置</a-button>
      </template>
      
      <a-row :gutter="24">
        <!-- 左侧：配置选择 -->
        <a-col :span="8">
          <div class="config-selector">
            <h4>选择表单配置</h4>
            <div class="config-list">
              <div
                v-for="config in configList"
                :key="config.id"
                class="config-item"
                :class="{ active: selectedConfig?.id === config.id }"
                @click="selectConfig(config)"
              >
                <div class="config-title">{{ config.confKey }}</div>
                <div class="config-desc">
                  字段数量: {{ config.parsedConfig.formSchema.length }}
                </div>
              </div>
            </div>
            
            <a-empty v-if="configList.length === 0" description="暂无动态表单配置" />
          </div>
        </a-col>

        <!-- 右侧：表单展示 -->
        <a-col :span="16">
          <div class="form-container">
            <div v-if="selectedConfig" class="form-wrapper">
              <h4>{{ selectedConfig.confKey }} - 表单</h4>
              
              <!-- 动态表单 -->
              <BasicForm @register="registerDynamicForm" />
              
              <!-- 操作按钮 -->
              <div class="form-actions">
                <a-button type="primary" @click="handleSubmit">提交表单</a-button>
                <a-button @click="handleReset">重置表单</a-button>
              </div>
              
              <!-- 配置信息 -->
              <a-collapse class="config-info">
                <a-collapse-panel key="1" header="查看配置详情">
                  <div class="config-detail">
                    <h5>表单结构:</h5>
                    <pre>{{ JSON.stringify(selectedConfig.parsedConfig.formSchema, null, 2) }}</pre>
                    
                    <h5>默认值:</h5>
                    <pre>{{ JSON.stringify(selectedConfig.parsedConfig.defaultValues, null, 2) }}</pre>
                  </div>
                </a-collapse-panel>
              </a-collapse>
            </div>
            
            <div v-else class="no-selection">
              <a-empty description="请选择一个表单配置" />
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 使用说明 -->
    <a-card title="使用说明" class="usage-card">
      <div class="usage-content">
        <h4>动态表单系统特点：</h4>
        <ul>
          <li><strong>可视化设计</strong>：通过表单设计器可视化创建表单结构</li>
          <li><strong>JSON存储</strong>：表单配置以JSON格式存储在数据库中</li>
          <li><strong>动态渲染</strong>：前端根据JSON配置动态生成表单</li>
          <li><strong>类型丰富</strong>：支持文本、数字、选择、开关等多种字段类型</li>
          <li><strong>灵活配置</strong>：支持必填验证、默认值、占位符等配置</li>
        </ul>

        <h4>操作步骤：</h4>
        <ol>
          <li>点击"设计表单"按钮打开表单设计器</li>
          <li>添加和配置表单字段</li>
          <li>保存配置到数据库</li>
          <li>在此演示页面选择配置并测试表单</li>
        </ol>
      </div>
    </a-card>
  </div>
</template>

<style scoped>
.dynamic-form-demo {
  padding: 20px;
}

.demo-card,
.usage-card {
  margin-bottom: 20px;
}

.config-selector {
  height: 500px;
}

.config-list {
  max-height: 400px;
  overflow-y: auto;
}

.config-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.config-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.config-item.active {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.config-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.config-desc {
  font-size: 12px;
  color: #666;
}

.form-container {
  min-height: 500px;
}

.form-wrapper {
  background: #fafafa;
  padding: 20px;
  border-radius: 6px;
}

.form-actions {
  margin: 20px 0;
  display: flex;
  gap: 12px;
}

.config-info {
  margin-top: 20px;
}

.config-detail pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.config-detail h5 {
  margin: 16px 0 8px 0;
  color: #333;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.usage-content ul,
.usage-content ol {
  padding-left: 20px;
}

.usage-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.usage-content h4 {
  margin: 16px 0 12px 0;
  color: #333;
}
</style>
