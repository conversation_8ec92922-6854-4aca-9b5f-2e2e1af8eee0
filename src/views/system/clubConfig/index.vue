<script lang="ts" setup>
import ConfigsModal from './ConfigsModal.vue'
import FormDesigner from './FormDesigner.vue'
import { columns, searchFormSchema } from './configs.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

import { deleteConfigs, exportConfigs, getConfigsPage } from '@/api/club/config'
import { DICT_TYPE } from '@/utils/dict'
import { getDictOptions } from '@/utils/dict'
import { ref } from 'vue'

defineOptions({ name: 'ClubConfigs' })

const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const [registerDesignerModal, { openModal: openDesignerModal }] = useModal()

// 添加 ref 声明
const configsModalRef = ref()

const [registerTable, { getForm, reload }] = useTable({
  title: '俱乐部参数配置列表',
  api: getConfigsPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 140,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleCreateDynamic() {
  openDesignerModal(true, {})
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportConfigs(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    }
  })
}

async function handleDelete(record: Recordable) {
  await deleteConfigs(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}

// 判断是否为JSON字符串
function isJson(str: string) {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}
</script>

<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['club:configs:create']" :preIcon="IconEnum.ADD" @click="handleCreateDynamic">
          新增
        </a-button>

        <a-button v-auth="['club:configs:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'confKey'">
          <span>
            {{getDictOptions(DICT_TYPE.SYSTEM_ConfKey_TYPE).find(item => item.value === record.confKey)?.label ||
              record.confKey}}
          </span>
        </template>
        <template v-else-if="column.key === 'confVal'">
          <div v-if="isJson(record.confVal)" class="json-preview">
            {{ record.confVal }}
          </div>
          <div v-else>
            {{ record.confVal }}
          </div>
        </template>
        <template v-else-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: t('action.edit'),
              auth: 'club:configs:update',
              onClick: handleEdit.bind(null, record)
            },
            {
              icon: IconEnum.DELETE,
              danger: true,
              label: t('action.delete'),
              auth: 'club:configs:delete',
              popConfirm: {
                title: t('common.delMessage'),
                placement: 'left',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <!-- 添加 ref 属性 -->
    <ConfigsModal @register="registerModal" @success="reload()" ref="configsModalRef" />
    <FormDesigner @register="registerDesignerModal" @success="reload()" />
  </div>
</template>

<style scoped>
.json-preview {
  max-width: 280px;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
